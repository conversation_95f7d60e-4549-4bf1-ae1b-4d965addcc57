<?php $__env->startSection('title', 'CLASSMOS - Section Management'); ?>

<?php $__env->startSection('main-content'); ?>
<link rel='stylesheet' href='<?php echo e(asset('plugins/select2/select2.css')); ?>'>
<section class="content-header">
    <h1><i class="fa fa-cubes"></i>
        Section Management
        <small><?php echo e($college->college_name); ?></small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="<?php echo e(url('/collegeadmin/dashboard')); ?>"><i class="fa fa-dashboard"></i> Home</a></li>
        <li class="active">Section Management</li>
    </ol>
</section>

<section class="content">
    <div class="container-fluid">
        <?php if(Session::has('success')): ?>
        <div class='col-sm-12'>
            <div class='alert alert-success alert-dismissible'>
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <i class="icon fa fa-check"></i> <?php echo e(Session::get('success')); ?>

            </div>
        </div>
        <?php endif; ?>

        <?php if(Session::has('error')): ?>
        <div class='col-sm-12'>
            <div class='alert alert-danger alert-dismissible'>
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <i class="icon fa fa-ban"></i> <?php echo e(Session::get('error')); ?>

            </div>
        </div>
        <?php endif; ?>

        <div class='row'>
            <div class='col-sm-5'>
                <div class='box box-solid box-primary'>
                    <div class='box-header with-border'>
                        <h3 class="box-title">New Section with Curriculum</h3>
                    </div>
                    <div class='box-body'>
                        <form action="<?php echo e(route('collegeadmin.section.create')); ?>" method="post">
                            <?php echo e(csrf_field()); ?>

                            
                            <div class='form-group'>
                                <label>College</label>
                                <input type="text" class="form-control" value="<?php echo e($college->college_code); ?> - <?php echo e($college->college_name); ?>" readonly>
                            </div>

                            <div class='form-group'>
                                <label>Program</label>
                                <select class="select2 form-control" name="program_code" id="program_code" required>
                                    <option value="">Please Select Program</option>
                                    <?php $__currentLoopData = $programs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($program['program_code']); ?>"><?php echo e($program['program_code']); ?> - <?php echo e($program['program_name']); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <div class='form-group'>
                                <label>Level</label>
                                <select name="level" id="level" required class='select2 form-control'>
                                    <option value="">Please Select Level</option>
                                    <option value="1st Year">1st Year</option>
                                    <option value="2nd Year">2nd Year</option>
                                    <option value="3rd Year">3rd Year</option>
                                    <option value="4th Year">4th Year</option>
                                    <option value="5th Year">5th Year</option>
                                </select>
                            </div>

                            <div class='form-group'>
                                <label>Section Name</label>
                                <input id="section_name" required name="section_name" type="text" class="form-control" placeholder="Enter section name">
                            </div>

                            <div class='form-group'>
                                <label>Add Curriculum Subjects</label>
                                <select name="add_curriculum" class="form-control" required>
                                    <option value="yes">Yes - Add all available curriculum subjects</option>
                                    <option value="no">No - Create empty section</option>
                                </select>
                            </div>

                            <div class='form-group'>
                                <button onclick='return confirm("Create this section? This action cannot be undone.")' type='submit' class='btn btn-flat btn-success btn-block'>
                                    <i class="fa fa-save"></i> Create Section
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class='col-sm-7'>
                <div class="box box-default">
                    <div class="box-header with-border">
                        <h3 class="box-title">Section List - <?php echo e($college->college_name); ?></h3>
                        <div class="box-tools pull-right">
                            <a href="<?php echo e(route('collegeadmin.section.archive')); ?>" class="btn btn-flat btn-warning">
                                <i class="fa fa-archive"></i> View Archives
                            </a>
                        </div>
                    </div>
                    <div class="box-body">
                        <!-- Search/Filter Section -->
                        <div class="row" style="margin-bottom: 15px;">
                            <div class="col-sm-4">
                                <select class="form-control" id="filter_program">
                                    <option value="">All Programs</option>
                                    <?php $__currentLoopData = $uniquePrograms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($program); ?>"><?php echo e($program); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <select class="form-control" id="filter_level">
                                    <option value="">All Levels</option>
                                    <?php $__currentLoopData = $uniqueLevels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($level); ?>"><?php echo e($level); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <input type="text" class="form-control" id="filter_section" placeholder="Section name...">
                            </div>
                            <div class="col-sm-2">
                                <button class="btn btn-flat btn-primary btn-block" onclick="searchSections()">
                                    <i class="fa fa-search"></i> Search
                                </button>
                            </div>
                        </div>

                        <div id="sections_table">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Program</th>
                                            <th>Level</th>
                                            <th>Section Name</th>
                                            <th>Subjects Count</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if(count($sections) > 0): ?>
                                        <?php $__currentLoopData = $sections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($section->program_code); ?></td>
                                            <td><?php echo e($section->level); ?></td>
                                            <td><?php echo e($section->section_name); ?></td>
                                            <td>
                                                <?php
                                                $offeringsCount = \App\offerings_infos_table::where('section_name', $section->section_name)->count();
                                                ?>
                                                <?php echo e($offeringsCount); ?>

                                            </td>
                                            <td>
                                                <a href="<?php echo e(route('collegeadmin.section.view', $section->id)); ?>" class="btn btn-flat btn-info btn-sm" title="View Section Details">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('collegeadmin.section.add_all', $section->id)); ?>" class="btn btn-flat btn-success btn-sm" title="Add All Available Curriculum Subjects" onclick="return confirm('Add all available curriculum subjects to this section?')">
                                                    <i class="fa fa-plus-circle"></i>
                                                </a>
                                                <button data-toggle="modal" data-target="#editModal" onclick="editSection('<?php echo e($section->id); ?>')" title="Edit Section" class="btn btn-flat btn-primary btn-sm">
                                                    <i class="fa fa-pencil"></i>
                                                </button>
                                                <a href="<?php echo e(route('collegeadmin.section.archive_section', $section->id)); ?>" class="btn btn-flat btn-danger btn-sm" title="Archive Section" onclick="return confirm('Do you wish to archive this section?')">
                                                    <i class="fa fa-archive"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                        <tr>
                                            <td colspan="5" class="text-center">No sections found for <?php echo e($college->college_name); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Edit Section Modal -->
<div id="editModal" class="modal fade" role="dialog">
    <div id='displayedit'></div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src='<?php echo e(asset('plugins/select2/select2.min.js')); ?>'></script>
<script>
$(document).ready(function() {
    $('.select2').select2();
});

// Auto-fill section name when program is selected
$('#program_code').on('change', function() {
    if (this.value != '' && this.value != 'Please Select Program') {
        $('#section_name').val(this.value + '-');
    } else {
        $('#section_name').val('');
    }
});

function editSection(section_id) {
    var array = {};
    array['section_id'] = section_id;
    $.ajax({
        type: "GET",
        url: "/ajax/collegeadmin/section_management/edit_section",
        data: array,
        success: function(data) {
            $('#displayedit').html(data).fadeIn();
            $('#editModal').modal('show');
        },
        error: function() {
            alert('Error loading section details');
        }
    });
}

function searchSections() {
    var program_code = $('#filter_program').val();
    var level = $('#filter_level').val();
    var section_name = $('#filter_section').val();

    $.ajax({
        type: "GET",
        url: "/ajax/collegeadmin/section_management/search",
        data: {
            program_code: program_code,
            level: level,
            section_name: section_name
        },
        success: function(data) {
            $('#sections_table').html(data);
        },
        error: function() {
            alert('Error searching sections');
        }
    });
}

// Search on Enter key
$('#filter_section').keypress(function(e) {
    if (e.which == 13) {
        searchSections();
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('vendor.adminlte.collegeadmin_layout.app', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>