<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>



<?php $__env->startSection('main-content'); ?>
<section class="content-header">
    <h1><i class="fa fa-user"></i>
        Faculty Details
        <small><?php echo e(Auth::user()->college_code); ?></small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="<?php echo e(url('/')); ?>"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="<?php echo e(route('collegeadmin.faculty.index')); ?>">Faculty Management</a></li>
        <li class="active">Faculty Details</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Faculty Information</h3>
                    <div class="box-tools pull-right">
                        <a href="<?php echo e(route('collegeadmin.faculty.edit', $faculty->id)); ?>" class="btn btn-primary btn-sm">
                            <i class="fa fa-pencil"></i> Edit
                        </a>
                    </div>
                </div>
                <div class="box-body">
                    <div class="row">
                        <!-- Personal Information -->
                        <div class="col-md-6">
                            <h4><i class="fa fa-user"></i> Personal Information</h4>
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 35%">Faculty ID</th>
                                    <td><?php echo e($faculty->username ?? $faculty->id); ?></td>
                                </tr>
                                <tr>
                                    <th>Full Name</th>
                                    <td>
                                        <strong><?php echo e(strtoupper($faculty->lastname)); ?>, <?php echo e(strtoupper($faculty->name)); ?></strong>
                                        <?php if($faculty->middlename): ?>
                                            <?php echo e(strtoupper($faculty->middlename)); ?>

                                        <?php endif; ?>
                                        <?php if($faculty->extensionname): ?>
                                            <?php echo e(strtoupper($faculty->extensionname)); ?>

                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td><?php echo e($faculty->email); ?></td>
                                </tr>
                                <tr>
                                    <th>College</th>
                                    <td><span class="label label-primary"><?php echo e($college->college_name ?? $faculty->college_code); ?></span></td>
                                </tr>
                                <?php if($info): ?>
                                <tr>
                                    <th>Gender</th>
                                    <td><?php echo e($info->gender ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Mobile Number</th>
                                    <td><?php echo e($info->cell_no ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Emergency Contact</th>
                                    <td><?php echo e($info->{'emerg_cont_#'} ?? 'N/A'); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>

                        <!-- Professional Information -->
                        <div class="col-md-6">
                            <h4><i class="fa fa-briefcase"></i> Professional Information</h4>
                            <?php if($info): ?>
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 35%">Department</th>
                                    <td><?php echo e($info->department ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Faculty Status</th>
                                    <td>
                                        <?php if($info->employee_type): ?>
                                            <span class="label label-success"><?php echo e($info->employee_type); ?></span>
                                        <?php else: ?>
                                            <span class="label label-default">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Program Graduated</th>
                                    <td><?php echo e($info->program_graduated ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Degree Status</th>
                                    <td><?php echo e($info->degree_status ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Account Status</th>
                                    <td>
                                        <?php if($faculty->is_first_login): ?>
                                            <span class="label label-warning">First Login Required</span>
                                        <?php else: ?>
                                            <span class="label label-success">Active</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                            <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"></i> No additional professional information available.
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <hr>

                    <!-- Teaching Load Section -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="box box-info">
                                <div class="box-header with-border">
                                    <h3 class="box-title"><i class="fa fa-calendar"></i> Current Teaching Load</h3>
                                    <div class="box-tools pull-right">
                                        <button type="button" class="btn btn-box-tool" onclick="refreshTeachingLoad()">
                                            <i class="fa fa-refresh"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="box-body">
                                    <div id="teaching-load">
                                        <p><i class="fa fa-spinner fa-spin"></i> Loading teaching load data...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="box-footer">
                    <a href="<?php echo e(route('collegeadmin.faculty.index')); ?>" class="btn btn-default">Back to List</a>
                    <a href="<?php echo e(route('collegeadmin.faculty_loading.generate_schedule', $faculty->id)); ?>" class="btn btn-success pull-right">
                        <i class="fa fa-calendar"></i> View Schedule
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('footer-script'); ?>
<script>
$(function() {
    loadTeachingLoad();
});

function loadTeachingLoad() {
    $('#teaching-load').html('<p><i class="fa fa-spinner fa-spin"></i> Loading teaching load data...</p>');

    $.ajax({
        url: "<?php echo e(url('/ajax/collegeadmin/faculty_loading/current_load')); ?>",
        type: 'GET',
        data: { instructor_id: <?php echo e($faculty->id); ?> },
        success: function(data) {
            if (data && data.trim() !== '') {
                $('#teaching-load').html(data);
            } else {
                $('#teaching-load').html('<div class="alert alert-info"><i class="fa fa-info-circle"></i> No current teaching load assigned.</div>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading teaching load:', error);
            $('#teaching-load').html('<div class="alert alert-danger"><i class="fa fa-exclamation-triangle"></i> Error loading teaching load data. Please try again.</div>');
        }
    });
}

function refreshTeachingLoad() {
    loadTeachingLoad();
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($layout, array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>